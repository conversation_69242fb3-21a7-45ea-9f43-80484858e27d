.text-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.text-editor-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #718096;
  font-size: 1.1rem;
}

/* Header */
.text-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-btn {
  background-color: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-btn:hover:not(:disabled) {
  background-color: #5a67d8;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.nav-btn:disabled {
  background-color: #cbd5e0;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.page-info {
  font-weight: 500;
  color: #4a5568;
  font-size: 0.9rem;
  min-width: 140px;
  text-align: center;
}

.text-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: #718096;
}

.text-stats span {
  background-color: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* Toolbar */
.text-editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: white;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
  gap: 1rem;
  flex-shrink: 0;
}

.formatting-tools,
.insert-tools,
.page-tools {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.format-btn {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  color: #4a5568;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: bold;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.format-btn:hover {
  background-color: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.format-btn:active {
  background-color: #667eea;
  color: white;
  border-color: #667eea;
}

.insert-btn {
  background-color: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.insert-btn:hover {
  background-color: #cbd5e0;
  transform: translateY(-1px);
}

.tool-btn {
  background-color: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tool-btn:hover {
  background-color: #cbd5e0;
  transform: translateY(-1px);
}

.tool-btn.danger {
  background-color: #fed7d7;
  color: #c53030;
}

.tool-btn.danger:hover {
  background-color: #feb2b2;
}

/* Content */
.text-editor-content {
  flex: 1;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

.text-editor-textarea {
  flex: 1;
  width: 100%;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 1rem;
  line-height: 1.6;
  color: #2d3748;
  background-color: white;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
}

.text-editor-textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.text-editor-textarea::placeholder {
  color: #a0aec0;
  font-style: italic;
}

/* Footer */
.text-editor-footer {
  padding: 0.75rem 1rem;
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.editing-tips {
  font-size: 0.8rem;
  color: #718096;
}

.editing-tips strong {
  color: #4a5568;
}

/* Responsive Design */
@media (max-width: 768px) {
  .text-editor-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }
  
  .text-stats {
    align-self: stretch;
    justify-content: space-between;
  }
  
  .text-editor-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .formatting-tools,
  .insert-tools,
  .page-tools {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .page-navigation {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .nav-btn {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .text-editor-content {
    padding: 0.75rem;
  }
  
  .text-editor-textarea {
    font-size: 0.9rem;
    padding: 0.75rem;
  }
  
  .format-btn {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }
  
  .insert-btn,
  .tool-btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
  }
}

/* Accessibility */
.format-btn:focus,
.insert-btn:focus,
.tool-btn:focus,
.nav-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Animation for button interactions */
.format-btn,
.insert-btn,
.tool-btn,
.nav-btn {
  position: relative;
  overflow: hidden;
}

.format-btn::after,
.insert-btn::after,
.tool-btn::after,
.nav-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.format-btn:active::after,
.insert-btn:active::after,
.tool-btn:active::after,
.nav-btn:active::after {
  width: 100px;
  height: 100px;
}
