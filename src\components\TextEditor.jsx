import { useState, useEffect } from 'react'
import './TextEditor.css'

const TextEditor = ({ pages, currentPage, onTextChange, onPageChange }) => {
  const [localText, setLocalText] = useState('')
  const [wordCount, setWordCount] = useState(0)
  const [charCount, setCharCount] = useState(0)

  useEffect(() => {
    if (pages && pages[currentPage]) {
      setLocalText(pages[currentPage].text)
    }
  }, [pages, currentPage])

  useEffect(() => {
    const words = localText.trim().split(/\s+/).filter(word => word.length > 0)
    setWordCount(words.length)
    setCharCount(localText.length)
  }, [localText])

  const handleTextChange = (event) => {
    const newText = event.target.value
    setLocalText(newText)
    onTextChange(currentPage, newText)
  }

  const goToPrevPage = () => {
    if (currentPage > 0) {
      onPageChange(currentPage - 1)
    }
  }

  const goToNextPage = () => {
    if (currentPage < pages.length - 1) {
      onPageChange(currentPage + 1)
    }
  }

  const insertText = (text) => {
    const textarea = document.querySelector('.text-editor-textarea')
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const newText = localText.substring(0, start) + text + localText.substring(end)
      setLocalText(newText)
      onTextChange(currentPage, newText)
      
      // Set cursor position after inserted text
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + text.length, start + text.length)
      }, 0)
    }
  }

  const formatText = (format) => {
    const textarea = document.querySelector('.text-editor-textarea')
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const selectedText = localText.substring(start, end)
      
      if (selectedText) {
        let formattedText = selectedText
        switch (format) {
          case 'bold':
            formattedText = `**${selectedText}**`
            break
          case 'italic':
            formattedText = `*${selectedText}*`
            break
          case 'underline':
            formattedText = `_${selectedText}_`
            break
          default:
            break
        }
        
        const newText = localText.substring(0, start) + formattedText + localText.substring(end)
        setLocalText(newText)
        onTextChange(currentPage, newText)
      }
    }
  }

  const clearPage = () => {
    if (window.confirm('Are you sure you want to clear all text on this page?')) {
      setLocalText('')
      onTextChange(currentPage, '')
    }
  }

  const restoreOriginal = () => {
    if (pages && pages[currentPage] && window.confirm('Restore original text for this page?')) {
      // In a real app, you'd have original text stored separately
      const originalText = `Original text for page ${currentPage + 1} would be restored here.`
      setLocalText(originalText)
      onTextChange(currentPage, originalText)
    }
  }

  if (!pages || pages.length === 0) {
    return (
      <div className="text-editor-placeholder">
        <p>No pages to edit</p>
      </div>
    )
  }

  return (
    <div className="text-editor">
      <div className="text-editor-header">
        <div className="page-navigation">
          <button 
            onClick={goToPrevPage} 
            disabled={currentPage === 0}
            className="nav-btn"
          >
            ← Previous
          </button>
          
          <span className="page-info">
            Editing Page {currentPage + 1} of {pages.length}
          </span>
          
          <button 
            onClick={goToNextPage} 
            disabled={currentPage === pages.length - 1}
            className="nav-btn"
          >
            Next →
          </button>
        </div>

        <div className="text-stats">
          <span>Words: {wordCount}</span>
          <span>Characters: {charCount}</span>
        </div>
      </div>

      <div className="text-editor-toolbar">
        <div className="formatting-tools">
          <button onClick={() => formatText('bold')} className="format-btn" title="Bold">
            <strong>B</strong>
          </button>
          <button onClick={() => formatText('italic')} className="format-btn" title="Italic">
            <em>I</em>
          </button>
          <button onClick={() => formatText('underline')} className="format-btn" title="Underline">
            <u>U</u>
          </button>
        </div>

        <div className="insert-tools">
          <button onClick={() => insertText('\n\n')} className="insert-btn">
            New Paragraph
          </button>
          <button onClick={() => insertText('• ')} className="insert-btn">
            Bullet Point
          </button>
        </div>

        <div className="page-tools">
          <button onClick={restoreOriginal} className="tool-btn">
            Restore Original
          </button>
          <button onClick={clearPage} className="tool-btn danger">
            Clear Page
          </button>
        </div>
      </div>

      <div className="text-editor-content">
        <textarea
          className="text-editor-textarea"
          value={localText}
          onChange={handleTextChange}
          placeholder="Start editing the text for this page..."
          spellCheck={true}
        />
      </div>

      <div className="text-editor-footer">
        <div className="editing-tips">
          <p><strong>Tips:</strong> Select text and use formatting buttons, or use keyboard shortcuts (Ctrl+B for bold, Ctrl+I for italic)</p>
        </div>
      </div>
    </div>
  )
}

export default TextEditor
