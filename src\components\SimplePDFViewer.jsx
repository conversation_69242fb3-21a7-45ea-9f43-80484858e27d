import { useState, useEffect } from 'react'
import './PDFViewer.css'

const SimplePDFViewer = ({ pdfFile, currentPage, onPageChange, totalPages }) => {
  const [pdfUrl, setPdfUrl] = useState(null)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (pdfFile) {
      try {
        const url = pdfFile?.url || (pdfFile instanceof File ? URL.createObjectURL(pdfFile) : pdfFile)
        setPdfUrl(url)
        setError(null)
      } catch (err) {
        console.error('Error creating PDF URL:', err)
        setError('Failed to load PDF file')
      }
    }

    return () => {
      // Cleanup URL if we created it
      if (pdfUrl && pdfFile instanceof File) {
        URL.revokeObjectURL(pdfUrl)
      }
    }
  }, [pdfFile])

  const goToPrevPage = () => {
    if (currentPage > 0) {
      onPageChange(currentPage - 1)
    }
  }

  const goToNextPage = () => {
    if (currentPage < totalPages - 1) {
      onPageChange(currentPage + 1)
    }
  }

  if (!pdfFile) {
    return (
      <div className="pdf-viewer-placeholder">
        <p>No PDF loaded</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="pdf-viewer">
        <div className="pdf-document-container">
          <div className="pdf-error">
            <h3>PDF Preview Error</h3>
            <p>{error}</p>
            <p>You can still edit the text content using the text editor panel.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="pdf-viewer">
      <div className="pdf-viewer-controls">
        <div className="page-controls">
          <button 
            onClick={goToPrevPage} 
            disabled={currentPage === 0}
            className="nav-btn"
          >
            ← Previous
          </button>
          
          <span className="page-info">
            Page {currentPage + 1} of {totalPages}
          </span>
          
          <button 
            onClick={goToNextPage} 
            disabled={currentPage === totalPages - 1}
            className="nav-btn"
          >
            Next →
          </button>
        </div>

        <div className="pdf-info">
          <span>Simple PDF Viewer</span>
        </div>
      </div>

      <div className="pdf-document-container">
        {pdfUrl ? (
          <iframe
            src={`${pdfUrl}#page=${currentPage + 1}&toolbar=0&navpanes=0&scrollbar=0`}
            width="100%"
            height="100%"
            style={{
              border: 'none',
              borderRadius: '8px',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)'
            }}
            title="PDF Preview"
            onError={() => setError('Failed to display PDF')}
          />
        ) : (
          <div className="pdf-loading">
            <div className="loading-spinner"></div>
            <p>Loading PDF...</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default SimplePDFViewer
