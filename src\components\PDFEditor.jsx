import { useState, useRef } from 'react'
import { PDFDocument, rgb } from 'pdf-lib'
import PDFViewer from './PDFViewer'
import TextEditor from './TextEditor'
import './PDFEditor.css'

const PDFEditor = () => {
  const [pdfFile, setPdfFile] = useState(null)
  const [pdfDocument, setPdfDocument] = useState(null)
  const [extractedText, setExtractedText] = useState([])
  const [editedText, setEditedText] = useState([])
  const [currentPage, setCurrentPage] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const fileInputRef = useRef(null)

  const handleFileUpload = async (event) => {
    const file = event.target.files[0]
    if (!file || file.type !== 'application/pdf') {
      setError('Please select a valid PDF file')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const arrayBuffer = await file.arrayBuffer()
      const pdfDoc = await PDFDocument.load(arrayBuffer)
      
      setPdfFile(file)
      setPdfDocument(pdfDoc)
      
      // Extract text from all pages
      await extractTextFromPDF(pdfDoc)
      
    } catch (err) {
      console.error('Error loading PDF:', err)
      setError('Failed to load PDF file. Please try another file.')
    } finally {
      setIsLoading(false)
    }
  }

  const extractTextFromPDF = async (pdfDoc) => {
    try {
      // For now, we'll create placeholder text for each page
      // In a real implementation, you'd use a library like pdf-parse or pdf2pic
      const pageCount = pdfDoc.getPageCount()
      const textData = []
      
      for (let i = 0; i < pageCount; i++) {
        const page = pdfDoc.getPage(i)
        const { width, height } = page.getSize()
        
        // Placeholder text - in a real app, you'd extract actual text
        textData.push({
          pageIndex: i,
          text: `This is placeholder text for page ${i + 1}.\n\nIn a real PDF editor, this would contain the actual extracted text from the PDF.\n\nYou can edit this text and it will be applied to the PDF when you save.`,
          width,
          height
        })
      }
      
      setExtractedText(textData)
      setEditedText(textData.map(page => ({ ...page })))
      
    } catch (err) {
      console.error('Error extracting text:', err)
      setError('Failed to extract text from PDF')
    }
  }

  const handleTextChange = (pageIndex, newText) => {
    setEditedText(prev => 
      prev.map(page => 
        page.pageIndex === pageIndex 
          ? { ...page, text: newText }
          : page
      )
    )
  }

  const savePDF = async () => {
    if (!pdfDocument) return

    setIsLoading(true)
    try {
      // Create a new PDF document
      const newPdfDoc = await PDFDocument.create()
      
      // Copy pages and add edited text
      for (let i = 0; i < editedText.length; i++) {
        const pageData = editedText[i]
        const page = newPdfDoc.addPage([pageData.width, pageData.height])
        
        // Add the edited text to the page
        page.drawText(pageData.text, {
          x: 50,
          y: pageData.height - 100,
          size: 12,
          color: rgb(0, 0, 0),
          maxWidth: pageData.width - 100,
        })
      }

      // Save the PDF
      const pdfBytes = await newPdfDoc.save()
      const blob = new Blob([pdfBytes], { type: 'application/pdf' })
      const url = URL.createObjectURL(blob)
      
      // Download the file
      const a = document.createElement('a')
      a.href = url
      a.download = `edited_${pdfFile.name}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
    } catch (err) {
      console.error('Error saving PDF:', err)
      setError('Failed to save PDF')
    } finally {
      setIsLoading(false)
    }
  }

  const resetEditor = () => {
    setPdfFile(null)
    setPdfDocument(null)
    setExtractedText([])
    setEditedText([])
    setCurrentPage(0)
    setError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="pdf-editor">
      {!pdfFile ? (
        <div className="upload-section">
          <div className="upload-area">
            <input
              ref={fileInputRef}
              type="file"
              accept=".pdf"
              onChange={handleFileUpload}
              className="file-input"
              id="pdf-upload"
            />
            <label htmlFor="pdf-upload" className="upload-label">
              <div className="upload-icon">📄</div>
              <h3>Upload PDF File</h3>
              <p>Click here or drag and drop a PDF file to start editing</p>
            </label>
          </div>
          {error && <div className="error-message">{error}</div>}
        </div>
      ) : (
        <div className="editor-layout">
          <div className="editor-header">
            <div className="file-info">
              <h3>{pdfFile.name}</h3>
              <span>{extractedText.length} pages</span>
            </div>
            <div className="editor-actions">
              <button onClick={savePDF} disabled={isLoading} className="save-btn">
                {isLoading ? 'Saving...' : 'Save PDF'}
              </button>
              <button onClick={resetEditor} className="reset-btn">
                New File
              </button>
            </div>
          </div>

          <div className="editor-content">
            <div className="pdf-viewer-section">
              <PDFViewer 
                pdfFile={pdfFile}
                currentPage={currentPage}
                onPageChange={setCurrentPage}
                totalPages={extractedText.length}
              />
            </div>
            
            <div className="text-editor-section">
              <TextEditor
                pages={editedText}
                currentPage={currentPage}
                onTextChange={handleTextChange}
                onPageChange={setCurrentPage}
              />
            </div>
          </div>

          {error && <div className="error-message">{error}</div>}
        </div>
      )}
      
      {isLoading && (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>Processing PDF...</p>
        </div>
      )}
    </div>
  )
}

export default PDFEditor
