{"version": 3, "file": "Keywords.js", "sourceRoot": "", "sources": ["../../../src/core/syntax/Keywords.ts"], "names": [], "mappings": ";AAAA,OAAO,SAAS,oBAAkC;AAE1C,IAAA,KAAK,GAA8B,SAAS,MAAvC,EAAE,cAAc,GAAc,SAAS,eAAvB,EAAE,OAAO,GAAK,SAAS,QAAd,CAAe;AAErD,IAAM,MAAM,GAAG;IACb,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;CACZ,CAAC;AAEF,IAAM,SAAS,GAAG;IAChB,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;IACX,SAAS,CAAC,CAAC;CACZ,CAAC;AAEF,MAAM,CAAC,IAAM,QAAQ,GAAG;IACtB,MAAM,EAAE;QACN,SAAS,CAAC,OAAO;QACjB,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,IAAI;KACf;IACD,GAAG,EAAE;QACH,SAAS,CAAC,OAAO;QACjB,SAAS,CAAC,OAAO;QACjB,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;KACZ;IACD,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;IAC5C,MAAM,EAAE;QACN,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;KACZ;IACD,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;IAC1D,OAAO,EAAE;QACP,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;KACZ;IACD,SAAS,EAAE;QACT,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;QACX,SAAS,CAAC,CAAC;KACZ;IACD,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;IAC1D,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;IACxE,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;IAC1D,MAAM,QAAA;IACN,UAAU,iBAAM,MAAM,GAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAC;IACvD,UAAU,iBAAM,MAAM,GAAE,cAAc,EAAE,OAAO,EAAC;IAChD,UAAU,iBAAM,MAAM,GAAE,cAAc,EAAC;IACvC,UAAU,iBAAM,MAAM,GAAE,OAAO,EAAC;IAChC,SAAS,WAAA;IACT,aAAa,kBAAG,cAAc,EAAE,OAAO,GAAK,SAAS,CAAC;IACtD,aAAa,kBAAG,cAAc,GAAK,SAAS,CAAC;IAC7C,aAAa,kBAAG,OAAO,GAAK,SAAS,CAAC;CACvC,CAAC"}