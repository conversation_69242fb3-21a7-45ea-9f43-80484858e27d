.pdf-editor {
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* Upload Section */
.upload-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.upload-area {
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  padding: 3rem;
  text-align: center;
  background-color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%;
  max-width: 500px;
}

.upload-area:hover {
  border-color: #667eea;
  background-color: #f7fafc;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.file-input {
  display: none;
}

.upload-label {
  cursor: pointer;
  display: block;
}

.upload-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.upload-label h3 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.upload-label p {
  margin: 0;
  color: #718096;
  font-size: 1rem;
}

/* Editor Layout */
.editor-layout {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.file-info h3 {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.file-info span {
  color: #718096;
  font-size: 0.9rem;
}

.editor-actions {
  display: flex;
  gap: 1rem;
}

.save-btn {
  background-color: #48bb78;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-btn:hover:not(:disabled) {
  background-color: #38a169;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.reset-btn {
  background-color: #e2e8f0;
  color: #4a5568;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-btn:hover {
  background-color: #cbd5e0;
  transform: translateY(-1px);
}

/* Editor Content */
.editor-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 600px;
}

.pdf-viewer-section {
  border-right: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.text-editor-section {
  background-color: white;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  color: white;
}

.loading-overlay p {
  margin-top: 1rem;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .editor-content {
    grid-template-columns: 1fr;
  }
  
  .pdf-viewer-section {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .editor-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .editor-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .pdf-editor {
    margin: 0;
  }
  
  .upload-area {
    padding: 2rem 1rem;
  }
  
  .editor-header {
    padding: 1rem;
  }
  
  .editor-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .save-btn,
  .reset-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Animation for smooth transitions */
.pdf-editor * {
  transition: all 0.2s ease;
}

/* Focus styles for accessibility */
.file-input:focus + .upload-label {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.save-btn:focus,
.reset-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}
