import { useState } from 'react'
import PDFEditor from './components/PDFEditor'
import PDFTest from './components/PDFTest'
import './App.css'

function App() {
  const [showTest, setShowTest] = useState(false)

  return (
    <div className="app">
      <header className="app-header">
        <h1>PDF Text Editor</h1>
        <p>Upload a PDF file and edit its text content</p>
        <button
          onClick={() => setShowTest(!showTest)}
          style={{ marginTop: '10px', padding: '5px 10px' }}
        >
          {showTest ? 'Hide Test' : 'Show PDF Test'}
        </button>
      </header>
      <main className="app-main">
        {showTest ? <PDFTest /> : <PDFEditor />}
      </main>
    </div>
  )
}

export default App
