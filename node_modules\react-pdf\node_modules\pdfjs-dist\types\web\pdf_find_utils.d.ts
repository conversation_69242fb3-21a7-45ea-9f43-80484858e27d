export namespace CharacterType {
    let SPACE: number;
    let ALPHA_LETTER: number;
    let PUNCT: number;
    let HAN_LETTER: number;
    let KATAKANA_LETTER: number;
    let HIRAGANA_LETTER: number;
    let HALFWIDTH_KATAKANA_LETTER: number;
    let THAI_LETTER: number;
}
/**
 * This function is based on the word-break detection implemented in:
 * https://hg.mozilla.org/mozilla-central/file/tip/intl/lwbrk/WordBreaker.cpp
 */
export function getCharacterType(charCode: any): number;
export function getNormalizeWithNFKC(): any;
