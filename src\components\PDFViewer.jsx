import { useState, useEffect, useRef } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import './PDFViewer.css'

// Set up the worker for react-pdf
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`

const PDFViewer = ({ pdfFile, currentPage, onPageChange, totalPages }) => {
  const [numPages, setNumPages] = useState(null)
  const [scale, setScale] = useState(1.0)
  const [isLoading, setIsLoading] = useState(false)
  const containerRef = useRef(null)

  useEffect(() => {
    if (containerRef.current) {
      const containerWidth = containerRef.current.offsetWidth
      const optimalScale = Math.min(containerWidth / 600, 1.5) // Assuming 600px as base width
      setScale(optimalScale)
    }
  }, [pdfFile])

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages)
    setIsLoading(false)
  }

  const onDocumentLoadError = (error) => {
    console.error('Error loading PDF:', error)
    setIsLoading(false)
  }

  const goToPrevPage = () => {
    if (currentPage > 0) {
      onPageChange(currentPage - 1)
    }
  }

  const goToNextPage = () => {
    if (currentPage < numPages - 1) {
      onPageChange(currentPage + 1)
    }
  }

  const zoomIn = () => {
    setScale(prev => Math.min(prev + 0.2, 3.0))
  }

  const zoomOut = () => {
    setScale(prev => Math.max(prev - 0.2, 0.5))
  }

  const resetZoom = () => {
    setScale(1.0)
  }

  if (!pdfFile) {
    return (
      <div className="pdf-viewer-placeholder">
        <p>No PDF loaded</p>
      </div>
    )
  }

  return (
    <div className="pdf-viewer" ref={containerRef}>
      <div className="pdf-viewer-controls">
        <div className="page-controls">
          <button 
            onClick={goToPrevPage} 
            disabled={currentPage === 0}
            className="nav-btn"
          >
            ← Previous
          </button>
          
          <span className="page-info">
            Page {currentPage + 1} of {numPages || totalPages}
          </span>
          
          <button 
            onClick={goToNextPage} 
            disabled={currentPage === (numPages || totalPages) - 1}
            className="nav-btn"
          >
            Next →
          </button>
        </div>

        <div className="zoom-controls">
          <button onClick={zoomOut} className="zoom-btn">-</button>
          <span className="zoom-level">{Math.round(scale * 100)}%</span>
          <button onClick={zoomIn} className="zoom-btn">+</button>
          <button onClick={resetZoom} className="reset-zoom-btn">Reset</button>
        </div>
      </div>

      <div className="pdf-document-container">
        {isLoading && (
          <div className="pdf-loading">
            <div className="loading-spinner"></div>
            <p>Loading PDF...</p>
          </div>
        )}
        
        <Document
          file={pdfFile}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          loading={
            <div className="pdf-loading">
              <div className="loading-spinner"></div>
              <p>Loading PDF...</p>
            </div>
          }
          className="pdf-document"
        >
          <Page
            pageNumber={currentPage + 1}
            scale={scale}
            loading={
              <div className="page-loading">
                <div className="loading-spinner"></div>
                <p>Loading page...</p>
              </div>
            }
            className="pdf-page"
          />
        </Document>
      </div>
    </div>
  )
}

export default PDFViewer
