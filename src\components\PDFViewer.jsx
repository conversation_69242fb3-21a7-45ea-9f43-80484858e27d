import { useState, useEffect, useRef } from 'react'
import './PDFViewer.css'

const PDFViewer = ({ pdfFile, currentPage, onPageChange, totalPages }) => {
  const [pdfUrl, setPdfUrl] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const containerRef = useRef(null)

  useEffect(() => {
    if (pdfFile) {
      setIsLoading(true)
      setError(null)

      try {
        let url
        if (pdfFile?.url) {
          url = pdfFile.url
        } else if (pdfFile instanceof File) {
          url = URL.createObjectURL(pdfFile)
        } else if (typeof pdfFile === 'string') {
          url = pdfFile
        } else {
          throw new Error('Invalid PDF file format')
        }

        setPdfUrl(url)
        setIsLoading(false)
        console.log('PDF URL created:', url)
      } catch (err) {
        console.error('Error creating PDF URL:', err)
        setError('Failed to load PDF file')
        setIsLoading(false)
      }
    }

    return () => {
      // Cleanup URL if we created it
      if (pdfUrl && pdfFile instanceof File) {
        URL.revokeObjectURL(pdfUrl)
      }
    }
  }, [pdfFile])

  const goToPrevPage = () => {
    if (currentPage > 0) {
      onPageChange(currentPage - 1)
    }
  }

  const goToNextPage = () => {
    if (currentPage < totalPages - 1) {
      onPageChange(currentPage + 1)
    }
  }

  if (!pdfFile) {
    return (
      <div className="pdf-viewer-placeholder">
        <p>No PDF loaded</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="pdf-viewer">
        <div className="pdf-viewer-controls">
          <div className="page-controls">
            <button
              onClick={goToPrevPage}
              disabled={currentPage === 0}
              className="nav-btn"
            >
              ← Previous
            </button>

            <span className="page-info">
              Page {currentPage + 1} of {totalPages}
            </span>

            <button
              onClick={goToNextPage}
              disabled={currentPage === totalPages - 1}
              className="nav-btn"
            >
              Next →
            </button>
          </div>
        </div>

        <div className="pdf-document-container">
          <div className="pdf-error">
            <h3>PDF Preview Error</h3>
            <p>{error}</p>
            <p>You can still edit the text content using the text editor panel.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="pdf-viewer" ref={containerRef}>
      <div className="pdf-viewer-controls">
        <div className="page-controls">
          <button
            onClick={goToPrevPage}
            disabled={currentPage === 0}
            className="nav-btn"
          >
            ← Previous
          </button>

          <span className="page-info">
            Page {currentPage + 1} of {totalPages}
          </span>

          <button
            onClick={goToNextPage}
            disabled={currentPage === totalPages - 1}
            className="nav-btn"
          >
            Next →
          </button>
        </div>

        <div className="pdf-info">
          <span>PDF Viewer</span>
        </div>
      </div>

      <div className="pdf-document-container">
        {isLoading ? (
          <div className="pdf-loading">
            <div className="loading-spinner"></div>
            <p>Loading PDF...</p>
          </div>
        ) : pdfUrl ? (
          <iframe
            src={`${pdfUrl}#page=${currentPage + 1}&toolbar=0&navpanes=0&scrollbar=1&zoom=page-fit`}
            width="100%"
            height="100%"
            style={{
              border: 'none',
              borderRadius: '8px',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
              backgroundColor: 'white'
            }}
            title="PDF Preview"
            onLoad={() => {
              console.log('PDF iframe loaded successfully')
              setError(null)
            }}
            onError={(e) => {
              console.error('PDF iframe error:', e)
              setError('Failed to display PDF in viewer')
            }}
          />
        ) : (
          <div className="pdf-loading">
            <div className="loading-spinner"></div>
            <p>Preparing PDF...</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default PDFViewer
