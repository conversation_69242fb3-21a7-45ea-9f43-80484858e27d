import { useState, useEffect, useRef } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import './PDFViewer.css'

// Set up the worker for react-pdf - try multiple fallbacks
try {
  pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'
} catch (error) {
  console.warn('Local worker failed, trying CDN:', error)
  pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`
}

const PDFViewer = ({ pdfFile, currentPage, onPageChange, totalPages }) => {
  const [numPages, setNumPages] = useState(null)
  const [scale, setScale] = useState(1.0)
  const [isLoading, setIsLoading] = useState(false)
  const [hasError, setHasError] = useState(false)
  const containerRef = useRef(null)

  useEffect(() => {
    if (containerRef.current) {
      const containerWidth = containerRef.current.offsetWidth
      const optimalScale = Math.min(containerWidth / 600, 1.5) // Assuming 600px as base width
      setScale(optimalScale)
    }
    // Reset error state when a new file is loaded
    setHasError(false)
  }, [pdfFile])

  const onDocumentLoadSuccess = ({ numPages }) => {
    console.log('PDF loaded successfully with', numPages, 'pages')
    setNumPages(numPages)
    setIsLoading(false)
  }

  const onDocumentLoadError = (error) => {
    console.error('Error loading PDF:', error)
    console.error('PDF file object:', pdfFile)
    console.error('PDF file URL:', pdfFile?.url || pdfFile)
    console.error('Error details:', {
      message: error.message,
      name: error.name,
      stack: error.stack
    })
    setHasError(true)
    setIsLoading(false)
  }

  const onPageLoadSuccess = () => {
    console.log('Page loaded successfully')
  }

  const onPageLoadError = (error) => {
    console.error('Error loading page:', error)
  }

  const goToPrevPage = () => {
    if (currentPage > 0) {
      onPageChange(currentPage - 1)
    }
  }

  const goToNextPage = () => {
    if (currentPage < numPages - 1) {
      onPageChange(currentPage + 1)
    }
  }

  const zoomIn = () => {
    setScale(prev => Math.min(prev + 0.2, 3.0))
  }

  const zoomOut = () => {
    setScale(prev => Math.max(prev - 0.2, 0.5))
  }

  const resetZoom = () => {
    setScale(1.0)
  }

  if (!pdfFile) {
    return (
      <div className="pdf-viewer-placeholder">
        <p>No PDF loaded</p>
      </div>
    )
  }

  if (hasError) {
    return (
      <div className="pdf-viewer">
        <div className="pdf-viewer-controls">
          <div className="page-controls">
            <button
              onClick={goToPrevPage}
              disabled={currentPage === 0}
              className="nav-btn"
            >
              ← Previous
            </button>

            <span className="page-info">
              Page {currentPage + 1} of {totalPages}
            </span>

            <button
              onClick={goToNextPage}
              disabled={currentPage === totalPages - 1}
              className="nav-btn"
            >
              Next →
            </button>
          </div>
        </div>

        <div className="pdf-document-container">
          <div className="pdf-error">
            <h3>PDF Preview Not Available</h3>
            <p>The PDF viewer encountered an error, but you can still edit the text content using the text editor panel.</p>
            <p>File: {pdfFile?.file?.name || pdfFile?.name || 'Unknown'}</p>
            <p>Use the page navigation above to switch between pages.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="pdf-viewer" ref={containerRef}>
      <div className="pdf-viewer-controls">
        <div className="page-controls">
          <button 
            onClick={goToPrevPage} 
            disabled={currentPage === 0}
            className="nav-btn"
          >
            ← Previous
          </button>
          
          <span className="page-info">
            Page {currentPage + 1} of {numPages || totalPages}
          </span>
          
          <button 
            onClick={goToNextPage} 
            disabled={currentPage === (numPages || totalPages) - 1}
            className="nav-btn"
          >
            Next →
          </button>
        </div>

        <div className="zoom-controls">
          <button onClick={zoomOut} className="zoom-btn">-</button>
          <span className="zoom-level">{Math.round(scale * 100)}%</span>
          <button onClick={zoomIn} className="zoom-btn">+</button>
          <button onClick={resetZoom} className="reset-zoom-btn">Reset</button>
        </div>
      </div>

      <div className="pdf-document-container">
        {isLoading && (
          <div className="pdf-loading">
            <div className="loading-spinner"></div>
            <p>Loading PDF...</p>
          </div>
        )}
        
        <Document
          file={(() => {
            const fileToLoad = pdfFile?.url || pdfFile
            console.log('Loading PDF file:', fileToLoad)
            return fileToLoad
          })()}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          loading={
            <div className="pdf-loading">
              <div className="loading-spinner"></div>
              <p>Loading PDF...</p>
            </div>
          }
          error={
            <div className="pdf-error">
              <p>Failed to load PDF. Please try a different file.</p>
            </div>
          }
          className="pdf-document"
        >
          <Page
            pageNumber={currentPage + 1}
            scale={scale}
            onLoadSuccess={onPageLoadSuccess}
            onLoadError={onPageLoadError}
            loading={
              <div className="page-loading">
                <div className="loading-spinner"></div>
                <p>Loading page...</p>
              </div>
            }
            error={
              <div className="page-error">
                <p>Failed to load page</p>
              </div>
            }
            className="pdf-page"
          />
        </Document>
      </div>
    </div>
  )
}

export default PDFViewer
