{"version": 3, "file": "Delimiters.js", "sourceRoot": "", "sources": ["../../../src/core/syntax/Delimiters.ts"], "names": [], "mappings": ";;;;AAAA,kEAAkD;AAErC,QAAA,WAAW,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;AAE/C,mBAAW,CAAC,mBAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACrC,mBAAW,CAAC,mBAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,mBAAW,CAAC,mBAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACpC,mBAAW,CAAC,mBAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;AACvC,mBAAW,CAAC,mBAAS,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAC7C,mBAAW,CAAC,mBAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAC9C,mBAAW,CAAC,mBAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACrC,mBAAW,CAAC,mBAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,mBAAW,CAAC,mBAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACxC,mBAAW,CAAC,mBAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC"}