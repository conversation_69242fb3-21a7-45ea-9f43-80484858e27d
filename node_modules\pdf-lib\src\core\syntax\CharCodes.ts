enum CharCodes {
  Null = 0,
  Backspace = 8,
  Tab = 9,
  Newline = 10,
  FormFeed = 12,
  CarriageReturn = 13,
  Space = 32,
  ExclamationPoint = 33,
  Hash = 35,
  Percent = 37,
  LeftParen = 40,
  RightParen = 41,
  Plus = 43,
  Minus = 45,
  Dash = 45,
  Period = 46,
  ForwardSlash = 47,
  <PERSON> = 48,
  One = 49,
  <PERSON> = 50,
  <PERSON> = 51,
  <PERSON> = 52,
  <PERSON> = 53,
  <PERSON> = 54,
  <PERSON> = 55,
  <PERSON> = 56,
  <PERSON> = 57,
  <PERSON><PERSON><PERSON> = 60,
  <PERSON><PERSON><PERSON> = 62,
  A = 65,
  D = 68,
  <PERSON> = 69,
  F = 70,
  O = 79,
  P = 80,
  R = 82,
  LeftSquareBracket = 91,
  BackSlash = 92,
  RightSquareBracket = 93,
  a = 97,
  b = 98,
  d = 100,
  e = 101,
  f = 102,
  i = 105,
  j = 106,
  l = 108,
  m = 109,
  n = 110,
  o = 111,
  r = 114,
  s = 115,
  t = 116,
  u = 117,
  x = 120,
  LeftCurly = 123,
  RightCurly = 125,
  Tilde = 126,
}

export default CharCodes;
