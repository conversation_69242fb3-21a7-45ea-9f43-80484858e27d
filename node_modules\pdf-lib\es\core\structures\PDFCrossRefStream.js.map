{"version": 3, "file": "PDFCrossRefStream.js", "sourceRoot": "", "sources": ["../../../src/core/structures/PDFCrossRefStream.ts"], "names": [], "mappings": ";AACA,OAAO,OAAO,2BAAiC;AAC/C,OAAO,MAAM,0BAAgC;AAE7C,OAAO,cAAc,yBAA2C;AAChE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,EAAE,oBAAkB;AAE5E,MAAM,CAAN,IAAY,SAIX;AAJD,WAAY,SAAS;IACnB,+CAAW,CAAA;IACX,yDAAgB,CAAA;IAChB,qDAAc,CAAA;AAChB,CAAC,EAJW,SAAS,KAAT,SAAS,QAIpB;AAyBD;;;;GAIG;AACH;IAAgC,qCAAc;IAe5C,2BAAoB,IAAa,EAAE,OAAiB,EAAE,MAAa;QAAb,uBAAA,EAAA,aAAa;QAAnE,YACE,kBAAM,IAAI,EAAE,MAAM,CAAC,SAQpB;QAgHD,yEAAyE;QACzE,+CAA+C;QAC/C,oDAAoD;QAC5C,kBAAY,GAAG;YACrB,IAAM,WAAW,GAAa,EAAE,CAAC;YAEjC,IAAI,gBAAgB,GAAG,CAAC,CAAC;YACzB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBAC7D,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBAExC,IAAI,GAAG,KAAK,CAAC,EAAE;oBACb,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;iBAC9C;qBAAM,IAAI,SAAS,CAAC,GAAG,CAAC,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,EAAE;oBACtE,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBACnC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;oBAC7C,gBAAgB,GAAG,CAAC,CAAC;iBACtB;gBAED,gBAAgB,IAAI,CAAC,CAAC;aACvB;YACD,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAEnC,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC;QAEM,wBAAkB,GAAG;YAC3B,IAAM,WAAW,GAAiB,IAAI,KAAK,CAAC,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAEjE,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBAC7D,IAAM,KAAK,GAAG,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAChC,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,OAAO,EAAE;oBAC5B,IAAA,IAAI,GAAgC,KAAK,KAArC,EAAE,oBAAoB,GAAU,KAAK,qBAAf,EAAE,GAAG,GAAK,KAAK,IAAV,CAAW;oBAClD,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,oBAAoB,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC;iBACvE;gBACD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,YAAY,EAAE;oBACjC,IAAA,IAAI,GAAkB,KAAK,KAAvB,EAAE,MAAM,GAAU,KAAK,OAAf,EAAE,GAAG,GAAK,KAAK,IAAV,CAAW;oBACpC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,gBAAgB,CAAC,CAAC;iBACzD;gBACD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,UAAU,EAAE;oBAC/B,IAAA,IAAI,GAA6B,KAAK,KAAlC,EAAE,eAAe,GAAY,KAAK,gBAAjB,EAAE,KAAK,GAAK,KAAK,MAAV,CAAW;oBAC/C,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;iBAChE;aACF;YAED,OAAO,WAAW,CAAC;QACrB,CAAC,CAAC;QAEM,+BAAyB,GAAG;YAClC,IAAM,WAAW,GAAG,KAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YACnD,IAAM,MAAM,GAA6B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAEnD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;gBACtD,IAAA,KAAyB,WAAW,CAAC,GAAG,CAAC,EAAxC,KAAK,QAAA,EAAE,MAAM,QAAA,EAAE,KAAK,QAAoB,CAAC;gBAEhD,IAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;gBACrC,IAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;gBACvC,IAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;gBAErC,IAAI,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC;oBAAE,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;gBACjD,IAAI,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC;oBAAE,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;gBACnD,IAAI,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC;oBAAE,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;aAClD;YAED,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;QAvLA,KAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QAC7B,KAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,WAAW,CAAC,KAAI,CAAC,kBAAkB,CAAC,CAAC;QACnE,KAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,WAAW,CAAC,KAAI,CAAC,yBAAyB,CAAC,CAAC;QAC5E,KAAI,CAAC,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC,KAAI,CAAC,YAAY,CAAC,CAAC;QAEvD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;;IACnD,CAAC;IAED,2CAAe,GAAf,UAAgB,GAAW,EAAE,oBAA4B;QACvD,IAAM,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,GAAG,KAAA,EAAE,oBAAoB,sBAAA,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC;IAED,gDAAoB,GAApB,UAAqB,GAAW,EAAE,MAAc;QAC9C,IAAM,IAAI,GAAG,SAAS,CAAC,YAAY,CAAC;QACpC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,GAAG,KAAA,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC;IAED,8CAAkB,GAAlB,UAAmB,GAAW,EAAE,eAAuB,EAAE,KAAa;QACpE,IAAM,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,GAAG,KAAA,EAAE,eAAe,iBAAA,EAAE,KAAK,OAAA,EAAE,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC;IAED,iCAAK,GAAL,UAAM,OAAoB;QAClB,IAAA,KAA4B,IAAI,EAA9B,IAAI,UAAA,EAAE,OAAO,aAAA,EAAE,MAAM,YAAS,CAAC;QACvC,OAAO,iBAAiB,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,CAAC;IAC5E,CAAC;IAED,6CAAiB,GAAjB;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;QACnD,IAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;QACpD,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,KACE,IAAI,QAAQ,GAAG,CAAC,EAAE,UAAU,GAAG,WAAW,CAAC,MAAM,EACjD,QAAQ,GAAG,UAAU,EACrB,QAAQ,EAAE,EACV;YACM,IAAA,KAAyB,WAAW,CAAC,QAAQ,CAAC,EAA7C,KAAK,QAAA,EAAE,MAAM,QAAA,EAAE,KAAK,QAAyB,CAAC;YAErD,IAAM,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YACjD,IAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YACnD,IAAM,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAEjD,KAAK,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;gBACjD,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC7C;YACD,KAAK,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;gBACjD,KAAK,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC9C;YACD,KAAK,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;gBACjD,KAAK,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC7C;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,gDAAoB,GAApB;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;QACnD,IAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;QACpD,IAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC;QAE/D,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,KACE,IAAI,QAAQ,GAAG,CAAC,EAAE,UAAU,GAAG,WAAW,CAAC,MAAM,EACjD,QAAQ,GAAG,UAAU,EACrB,QAAQ,EAAE,EACV;YACM,IAAA,KAAyB,WAAW,CAAC,QAAQ,CAAC,EAA7C,KAAK,QAAA,EAAE,MAAM,QAAA,EAAE,KAAK,QAAyB,CAAC;YAErD,IAAM,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YACjD,IAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YACnD,IAAM,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAEjD,KAAK,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;gBACjD,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACzC;YACD,KAAK,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;gBACjD,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAC1C;YACD,KAAK,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;gBACjD,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACzC;SACF;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,oDAAwB,GAAxB;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;QACpD,IAAM,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC;QACnC,OAAO,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IAC1C,CAAC;IAED,sCAAU,GAAV;QACE,iBAAM,UAAU,WAAE,CAAC;QAEnB,IAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;QACpD,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QAE/B,IAAA,OAAO,GAAK,IAAI,CAAC,IAAI,QAAd,CAAe;QAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;IACzD,CAAC;IArIM,wBAAM,GAAG,UAAC,IAAa,EAAE,MAAa;QAAb,uBAAA,EAAA,aAAa;QAC3C,IAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;QACvD,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEK,oBAAE,GAAG,UAAC,IAAa,EAAE,OAAgB,EAAE,MAAa;QAAb,uBAAA,EAAA,aAAa;QACzD,OAAA,IAAI,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC;IAA5C,CAA4C,CAAC;IAkMjD,wBAAC;CAAA,AA1MD,CAAgC,cAAc,GA0M7C;AAED,eAAe,iBAAiB,CAAC"}