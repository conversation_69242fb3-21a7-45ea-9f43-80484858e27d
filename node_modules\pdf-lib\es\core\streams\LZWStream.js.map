{"version": 3, "file": "LZWStream.js", "sourceRoot": "", "sources": ["../../../src/core/streams/LZWStream.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;;AAEH,OAAO,YAAY,uBAAsC;AAGzD;IAAwB,6BAAY;IAgBlC,mBACE,MAAkB,EAClB,WAA+B,EAC/B,WAAkB;QAHpB,YAKE,kBAAM,WAAW,CAAC,SAsBnB;QApBC,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,KAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,KAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QAEpB,IAAM,oBAAoB,GAAG,IAAI,CAAC;QAClC,IAAM,QAAQ,GAAG;YACf,WAAW,aAAA;YACX,UAAU,EAAE,CAAC;YACb,QAAQ,EAAE,GAAG;YACb,gBAAgB,EAAE,IAAI,UAAU,CAAC,oBAAoB,CAAC;YACtD,iBAAiB,EAAE,IAAI,WAAW,CAAC,oBAAoB,CAAC;YACxD,mBAAmB,EAAE,IAAI,WAAW,CAAC,oBAAoB,CAAC;YAC1D,eAAe,EAAE,IAAI,UAAU,CAAC,oBAAoB,CAAC;YACrD,qBAAqB,EAAE,CAAC;SACzB,CAAC;QACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE;YAC5B,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACjC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACnC;QACD,KAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;;IAC3B,CAAC;IAES,6BAAS,GAAnB;QACE,IAAM,SAAS,GAAG,GAAG,CAAC;QAEtB,IAAI,oBAAoB,GAAG,SAAS,GAAG,CAAC,CAAC;QACzC,IAAM,gBAAgB,GAAG,SAAS,CAAC;QACnC,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QAEN,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,CAAC,gBAAgB;SACzB;QAED,IAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QACzC,IAAI,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACjC,IAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;QACnD,IAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC;QACrD,IAAM,mBAAmB,GAAG,QAAQ,CAAC,mBAAmB,CAAC;QACzD,IAAI,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QACrC,IAAI,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACjC,IAAM,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;QACjD,IAAI,qBAAqB,GAAG,QAAQ,CAAC,qBAAqB,CAAC;QAE3D,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC;QAC5C,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,GAAG,oBAAoB,CAAC,CAAC;QAEzE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;YAC9B,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YACvC,IAAM,OAAO,GAAG,qBAAqB,GAAG,CAAC,CAAC;YAC1C,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,EAAE;gBACvB,eAAe,CAAC,CAAC,CAAC,GAAG,IAAc,CAAC;gBACpC,qBAAqB,GAAG,CAAC,CAAC;aAC3B;iBAAM,IAAI,IAAI,IAAI,GAAG,EAAE;gBACtB,IAAI,IAAI,GAAG,QAAQ,EAAE;oBACnB,qBAAqB,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBAChD,KAAK,CAAC,GAAG,qBAAqB,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;wBACzD,eAAe,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;wBACzC,CAAC,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC;qBAC5B;iBACF;qBAAM;oBACL,eAAe,CAAC,qBAAqB,EAAE,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;iBAC/D;aACF;iBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;gBACvB,UAAU,GAAG,CAAC,CAAC;gBACf,QAAQ,GAAG,GAAG,CAAC;gBACf,qBAAqB,GAAG,CAAC,CAAC;gBAC1B,SAAS;aACV;iBAAM;gBACL,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;gBAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;gBACrB,MAAM;aACP;YAED,IAAI,OAAO,EAAE;gBACX,mBAAmB,CAAC,QAAQ,CAAC,GAAG,QAAkB,CAAC;gBACnD,iBAAiB,CAAC,QAAQ,CAAC,GAAG,iBAAiB,CAAC,QAAkB,CAAC,GAAG,CAAC,CAAC;gBACxE,gBAAgB,CAAC,QAAQ,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;gBAChD,QAAQ,EAAE,CAAC;gBACX,UAAU;oBACR,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,GAAG,WAAW,GAAG,CAAC,CAAC;wBACrD,CAAC,CAAC,UAAU;wBACZ,CAAC,CAAC,IAAI,CAAC,GAAG,CACN,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,kBAAkB,GAAG,CAAC,EACzD,EAAE,CACH,GAAG,CAAC,CAAC;aACb;YACD,QAAQ,GAAG,IAAI,CAAC;YAEhB,aAAa,IAAI,qBAAqB,CAAC;YACvC,IAAI,oBAAoB,GAAG,aAAa,EAAE;gBACxC,GAAG;oBACD,oBAAoB,IAAI,gBAAgB,CAAC;iBAC1C,QAAQ,oBAAoB,GAAG,aAAa,EAAE;gBAC/C,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,GAAG,oBAAoB,CAAC,CAAC;aACtE;YACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,EAAE,CAAC,EAAE,EAAE;gBAC1C,MAAM,CAAC,mBAAmB,EAAE,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;aACpD;SACF;QACD,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC7B,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;QACjC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC7B,QAAQ,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;QAEvD,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC;IAC1C,CAAC;IAEO,4BAAQ,GAAhB,UAAiB,CAAS;QACxB,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,OAAO,UAAU,GAAG,CAAC,EAAE;YACrB,IAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;gBAChB,OAAO,IAAI,CAAC;aACb;YACD,UAAU,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACnC,UAAU,IAAI,CAAC,CAAC;SACjB;QACD,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,OAAO,CAAC,UAAU,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACtD,CAAC;IACH,gBAAC;AAAD,CAAC,AAtJD,CAAwB,YAAY,GAsJnC;AAED,eAAe,SAAS,CAAC"}