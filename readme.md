# PDF Text Editor

A modern web-based PDF editor that allows you to edit text content in PDF files. Built with React, Vite, and PDF-lib.

## Features

- **PDF Upload**: Upload PDF files through a drag-and-drop interface
- **PDF Viewer**: View PDF pages with zoom controls and navigation
- **Text Editing**: Edit text content with a rich text editor
- **Page Navigation**: Navigate between multiple pages
- **Text Formatting**: Basic text formatting options (bold, italic, underline)
- **Export**: Save modified PDF files
- **Responsive Design**: Works on desktop and mobile devices

## Getting Started

### Prerequisites

- Node.js (version 20.12.0 or higher)
- npm or yarn

### Installation

1. Clone the repository or download the source code
2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

### Usage

1. **Upload a PDF**: Click on the upload area or drag and drop a PDF file
2. **View PDF**: The PDF will be displayed in the left panel with navigation controls
3. **Edit Text**: Use the text editor in the right panel to modify content
4. **Navigate Pages**: Use the navigation buttons to switch between pages
5. **Format Text**: Select text and use formatting buttons for styling
6. **Save Changes**: Click "Save PDF" to download the modified file

### Sample PDF

A sample PDF file (`sample.pdf`) is included for testing purposes. You can use this file to test the editor's functionality.

## Technology Stack

- **React 19**: Frontend framework
- **Vite**: Build tool and development server
- **PDF-lib**: PDF manipulation library
- **react-pdf**: PDF viewing component
- **CSS3**: Styling with modern CSS features

## Project Structure

```
src/
├── components/
│   ├── PDFEditor.jsx      # Main editor component
│   ├── PDFViewer.jsx      # PDF display component
│   ├── TextEditor.jsx     # Text editing component
│   └── *.css              # Component styles
├── App.jsx                # Main application component
├── main.jsx              # Application entry point
└── index.css             # Global styles
```

## Features in Detail

### PDF Viewer
- Zoom in/out controls
- Page navigation
- Responsive design
- Loading states

### Text Editor
- Multi-page text editing
- Word and character count
- Text formatting tools
- Insert tools (paragraphs, bullet points)
- Page-specific editing

### File Management
- Drag and drop upload
- File validation
- Error handling
- Progress indicators

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Known Limitations

- Text extraction is currently placeholder-based (in a production version, you would integrate with a proper PDF text extraction library)
- Complex PDF layouts may not be fully supported
- Some PDF features like forms and annotations are not supported

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For issues and questions, please create an issue in the repository or contact the development team.
