import { useState } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'

// Set up the worker for react-pdf
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'

const PDFTest = () => {
  const [numPages, setNumPages] = useState(null)
  const [pageNumber, setPageNumber] = useState(1)
  const [error, setError] = useState(null)

  function onDocumentLoadSuccess({ numPages }) {
    setNumPages(numPages)
    setError(null)
    console.log('PDF loaded successfully with', numPages, 'pages')
  }

  function onDocumentLoadError(error) {
    setError(error.message)
    console.error('PDF load error:', error)
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>PDF Test Component</h2>
      
      {error && (
        <div style={{ color: 'red', marginBottom: '20px' }}>
          Error: {error}
        </div>
      )}

      <div style={{ border: '1px solid #ccc', padding: '20px', marginBottom: '20px' }}>
        <Document
          file="/sample.pdf"
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          loading={<div>Loading PDF...</div>}
          error={<div>Failed to load PDF</div>}
        >
          <Page 
            pageNumber={pageNumber} 
            loading={<div>Loading page...</div>}
            error={<div>Failed to load page</div>}
          />
        </Document>
      </div>

      {numPages && (
        <div>
          <p>
            Page {pageNumber} of {numPages}
          </p>
          <button 
            disabled={pageNumber <= 1} 
            onClick={() => setPageNumber(pageNumber - 1)}
          >
            Previous
          </button>
          <button 
            disabled={pageNumber >= numPages} 
            onClick={() => setPageNumber(pageNumber + 1)}
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}

export default PDFTest
