.pdf-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
}

.pdf-viewer-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #718096;
  font-size: 1.1rem;
}

/* Controls */
.pdf-viewer-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: white;
  border-bottom: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.page-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-btn {
  background-color: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-btn:hover:not(:disabled) {
  background-color: #5a67d8;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.nav-btn:disabled {
  background-color: #cbd5e0;
  color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.page-info {
  font-weight: 500;
  color: #4a5568;
  font-size: 0.9rem;
  min-width: 120px;
  text-align: center;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.zoom-btn {
  background-color: #e2e8f0;
  color: #4a5568;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: bold;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-btn:hover {
  background-color: #cbd5e0;
  transform: translateY(-1px);
}

.zoom-level {
  font-size: 0.9rem;
  font-weight: 500;
  color: #4a5568;
  min-width: 50px;
  text-align: center;
}

.reset-zoom-btn {
  background-color: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.reset-zoom-btn:hover {
  background-color: #cbd5e0;
  transform: translateY(-1px);
}

/* Document Container */
.pdf-document-container {
  flex: 1;
  overflow: auto;
  padding: 1rem;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: linear-gradient(45deg, #f8fafc 25%, transparent 25%), 
              linear-gradient(-45deg, #f8fafc 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #f8fafc 75%), 
              linear-gradient(-45deg, transparent 75%, #f8fafc 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.pdf-document {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
}

.pdf-page {
  display: block !important;
  margin: 0 auto;
}

/* Loading States */
.pdf-loading,
.page-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #718096;
}

.pdf-loading {
  min-height: 300px;
}

.page-loading {
  min-height: 200px;
}

.pdf-loading p,
.page-loading p {
  margin-top: 1rem;
  font-size: 1rem;
}

/* Error States */
.pdf-error,
.page-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #e53e3e;
  background-color: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  margin: 1rem;
}

.pdf-error {
  min-height: 300px;
}

.page-error {
  min-height: 200px;
}

.pdf-error p,
.page-error p {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pdf-viewer-controls {
    flex-direction: column;
    gap: 1rem;
    padding: 0.75rem;
  }
  
  .page-controls {
    order: 2;
  }
  
  .zoom-controls {
    order: 1;
  }
  
  .nav-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.8rem;
  }
  
  .page-info {
    font-size: 0.8rem;
    min-width: 100px;
  }
  
  .pdf-document-container {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .page-controls {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .zoom-controls {
    justify-content: center;
  }
  
  .nav-btn {
    width: 100%;
    max-width: 200px;
  }
}

/* Accessibility */
.nav-btn:focus,
.zoom-btn:focus,
.reset-zoom-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Smooth scrolling */
.pdf-document-container {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
.pdf-document-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.pdf-document-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.pdf-document-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.pdf-document-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}
